package com.xiaozhi.communication.domain;

import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.entity.SysRole;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RoleWithConfig {
    private SysRole role;

    /**
     * tts speed
     */
    private double speed;

    private SysConfig sttConfig;
    private SysConfig ttsConfig;
    private SysConfig modelConfig;

    public Integer getSttId() {
        return role.getSttId();
    }

    public Integer getTtsId() {
        return role.getTtsId();
    }

    public Integer getModelId() {
        return role.getModelId();
    }

    public String getVoice() {
        return role.getVoice();
    }
}
