package com.xiaozhi.dialogue.stt.providers;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import reactor.core.publisher.Sinks;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
public class YoudaoService implements SttService {

    private final String apiUrl;
    private final String appId;
    private final String appSecret;

    public YoudaoService(SysConfig config) {
        this.apiUrl = config.getApiUrl();
        this.appId = config.getAppId();
        this.appSecret = config.getApiSecret();
    }

    @Override
    public String getProviderName() {
        return "youdao";
    }

    @Override
    public String recognition(byte[] audioData) {
        return "";
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        var recognitionLatch = new CountDownLatch(1);
        var isRunning = new AtomicBoolean(true);
        var finalResult = new AtomicReference<>("");
        var responseQueue = new LinkedBlockingQueue<String>();

        var audioData = audioSink.asFlux()
                .reduce(new byte[0], (acc, next) -> {
                    var combined = new byte[acc.length + next.length];
                    System.arraycopy(acc, 0, combined, 0, acc.length);
                    System.arraycopy(next, 0, combined, acc.length, next.length);
                    return combined;
                }).block();

        var params = createRequestParams();
        addAuthParams(appId, appSecret, params);
        var query = new StringBuilder();
        for (var entry : params.entrySet()) {
            var key = entry.getKey();
            var values = entry.getValue();
            for (var value : values) {
                query.append(key).append("=").append(value).append("&");
            }
        }
        query.deleteCharAt(query.length() - 1);

        var client = new OkHttpClient.Builder().build();
        var request = new Request.Builder()
                .url(STR."\{apiUrl}?\{query}")
                .build();

        var webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onMessage(@NotNull WebSocket webSocket, @NotNull ByteString bytes) {
                log.info("bytestring length {}", bytes.size());
            }

            @Override
            public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
                log.info("Received Message {}", text);
                responseQueue.offer(text);
            }

            @Override
            public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                log.info("websocket closing {}", reason);
                isRunning.set(false);
            }

            @Override
            public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                log.info("websocket closing {}", reason);
                isRunning.set(false);
            }

            @Override
            public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
                isRunning.set(false);
                recognitionLatch.countDown();
            }

            @Override
            public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
                log.info("Youdao websocket opened");

                Thread.startVirtualThread(() -> {
                    var wavHeader = AudioUtils.buildWavHeader(audioData, (short) 16);
                    webSocket.send(ByteString.of(wavHeader));

                    var offset = 0;
                    final var CHUNK = 6400;
                    var total = audioData.length;

                    while (offset < total) {
                        var len = Math.min(CHUNK, total - offset);
                        log.info("SEND CHUNK {}", CHUNK);
                        webSocket.send(ByteString.of(audioData, offset, len));
                        offset += len;
                    }

                    webSocket.send(ByteString.of("{\"end\":\"true\"}".getBytes()));

                    try {
                        TimeUnit.MILLISECONDS.sleep(3000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    webSocket.close(1000, "done");
                });
            }
        });

        // 启动响应处理线程
        Thread.startVirtualThread(() -> {
            log.info("Start new thread process response");
            try {
                processResponses(responseQueue, isRunning, finalResult, recognitionLatch);
            } catch (Exception e) {
                isRunning.set(false);
                log.error("处理响应时发生错误", e);
            }
        });

        try {
            // 等待识别完成或超时
            boolean recognized = recognitionLatch.await(30 * 1000, TimeUnit.MILLISECONDS);
            if (!recognized) {
                log.warn("有道ASR识别超时");
            }
        } catch (InterruptedException e) {
            log.error("识别过程被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 清理资源
            // cleanup(webSocket, client);
        }

        return finalResult.get();
    }


    /**
     * 处理响应消息
     */
    private void processResponses(BlockingQueue<String> responseQueue, AtomicBoolean isRunning, AtomicReference<String> finalResult, CountDownLatch recognitionLatch) {
        while (isRunning.get() || !responseQueue.isEmpty()) {
            try {
                var response = responseQueue.poll(100, TimeUnit.MILLISECONDS);
                if (response != null) {
                    log.info("response is ===> {}", response);
                    JsonUtil.parse(response, RecognitionResult.class)
                            .filter(it -> it.getErrorCode() == 0)
                            .onFailure(e -> log.error("Recognize failed {}", e.getMessage()))
                            .forEach(res -> {
                                for (var it : res.getResult()) {
                                    if (!it.getSt().getPartial()) {
                                        log.info("Recognition result is {}", it.getSt().getSentence());
                                        finalResult.set(it.getSt().getSentence());
                                        break;
                                    }
                                }
                            });
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        recognitionLatch.countDown();
    }

    private Map<String, String[]> createRequestParams() {
        /*
         * note: 将下列变量替换为需要请求的参数
         * 取值参考文档: https://ai.youdao.com/DOCSIRMA/html/tts/api/ssyysb/index.html
         * langType: https://ai.youdao.com/DOCSIRMA/html/tts/api/dyysb/index.html#section-8
         */
        var langType = "zh-CHS";
        var rate = "16000";

        return new HashMap<>() {{
            put("langType", new String[]{langType});
            put("rate", new String[]{rate});
            put("format", new String[]{"wav"});
            put("channel", new String[]{"1"});
            put("version", new String[]{"v1"});
        }};
    }


    /**
     * 添加鉴权相关参数 -
     *
     * @param appId     您的应用ID
     * @param appSecret 您的应用密钥
     * @param paramsMap 请求参数表
     */
    public void addAuthParams(String appId, String appSecret, Map<String, String[]> paramsMap) {
        var salt = UUID.randomUUID().toString();
        var timestamp = String.valueOf(System.currentTimeMillis() / 1000);

        // sign = sha256(appId + salt + timestamp + appSecret)
        var strSrc = appId + salt + timestamp + appSecret;
        var sign = encrypt(strSrc);

        paramsMap.put("appKey", new String[]{appId});
        paramsMap.put("salt", new String[]{salt});
        paramsMap.put("curtime", new String[]{timestamp});
        paramsMap.put("signType", new String[]{"v4"});
        paramsMap.put("sign", new String[]{sign});
    }

    private String encrypt(String strSrc) {
        try {
            var md = MessageDigest.getInstance("SHA-256");
            md.update(strSrc.getBytes());
            var des = new StringBuilder();
            for (byte b : md.digest()) {
                var tmp = (Integer.toHexString(b & 0xFF));
                if (tmp.length() == 1) {
                    des.append("0");
                }
                des.append(tmp);
            }
            return des.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    @Data
    static class RecognitionResult {
        private List<Result> result;
        @JsonProperty("errorCode")
        private int errorCode;
    }

    @Data
    static class Result {
        private int segId;
        private St st;
    }

    @Data
    static class St {
        private String sentence;
        private Boolean partial;
    }
}
