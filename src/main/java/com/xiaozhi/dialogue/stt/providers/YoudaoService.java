package com.xiaozhi.dialogue.stt.providers;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import reactor.core.publisher.Sinks;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class YoudaoService implements SttService {

    private static final String PROVIDER_NAME = "youdao";
    private static final String DEFAULT_API_URL = "wss://openapi.youdao.com/stream_asropenapi";
    private static final int RECOGNITION_TIMEOUT_MS = 30000; // 30秒超时
    private static final int CHUNK_SIZE = 6400; // 音频块大小
    private static final int QUEUE_TIMEOUT_MS = 100;
    private static final int SEND_INTERVAL_MS = 200; // 建议发送间隔

    private final String apiUrl;
    private final String appId;
    private final String appSecret;

    public YoudaoService(SysConfig config) {
        this.apiUrl = config.getApiUrl() != null ? config.getApiUrl() : DEFAULT_API_URL;
        this.appId = config.getAppId();
        this.appSecret = config.getApiSecret();
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String recognition(byte[] audioData) {
        log.warn("有道ASR不支持单次识别，请使用流式识别");
        return null;
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        // 检查配置参数
        if (appId == null || appSecret == null) {
            log.error("有道ASR配置未设置，无法进行识别");
            return "";
        }

        var recognitionLatch = new CountDownLatch(1);
        var isRunning = new AtomicBoolean(true);
        var finalResult = new AtomicReference<>("");
        var responseQueue = new LinkedBlockingQueue<String>();

        // 收集音频数据
        var audioData = audioSink.asFlux()
                .reduce(new byte[0], (acc, next) -> {
                    var combined = new byte[acc.length + next.length];
                    System.arraycopy(acc, 0, combined, 0, acc.length);
                    System.arraycopy(next, 0, combined, acc.length, next.length);
                    return combined;
                }).block();

        if (audioData == null || audioData.length == 0) {
            log.warn("音频数据为空");
            return "";
        }

        // 构建请求参数
        var params = createRequestParams();
        addAuthParams(appId, appSecret, params);
        var queryString = buildQueryString(params);

        // 创建WebSocket客户端
        var client = new OkHttpClient.Builder()
                .pingInterval(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .callTimeout(60, TimeUnit.SECONDS)
                .build();

        var request = new Request.Builder()
                .url(apiUrl + "?" + queryString)
                .build();

        var webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
                log.debug("收到识别结果: {}", text);
                responseQueue.offer(text);
            }

            @Override
            public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                log.info("WebSocket正在关闭: {}", reason);
                isRunning.set(false);
            }

            @Override
            public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                log.info("WebSocket已关闭: {}", reason);
                isRunning.set(false);
            }

            @Override
            public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
                log.error("WebSocket连接失败", t);
                isRunning.set(false);
                recognitionLatch.countDown();
            }

            @Override
            public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
                log.info("有道ASR WebSocket连接已建立");

                // 在新线程中发送音频数据
                Thread.startVirtualThread(() -> {
                    try {
                        sendAudioData(webSocket, audioData);
                    } catch (Exception e) {
                        log.error("发送音频数据时发生错误", e);
                        isRunning.set(false);
                        recognitionLatch.countDown();
                    }
                });
            }
        });

        // 启动响应处理线程
        Thread.startVirtualThread(() -> {
            try {
                processResponses(responseQueue, isRunning, finalResult, recognitionLatch);
            } catch (Exception e) {
                log.error("处理响应时发生错误", e);
                isRunning.set(false);
                recognitionLatch.countDown();
            }
        });

        try {
            // 等待识别完成或超时
            boolean recognized = recognitionLatch.await(RECOGNITION_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!recognized) {
                log.warn("有道ASR识别超时");
            }
        } catch (InterruptedException e) {
            log.error("识别过程被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 清理资源
            cleanup(webSocket, client);
        }

        return finalResult.get();
    }

    /**
     * 发送音频数据到WebSocket
     */
    private void sendAudioData(WebSocket webSocket, byte[] audioData) throws InterruptedException {
        // 发送WAV头部
        var wavHeader = AudioUtils.buildWavHeader(audioData, (short) 16);
        webSocket.send(ByteString.of(wavHeader));
        log.debug("发送WAV头部，大小: {}", wavHeader.length);

        // 分块发送音频数据
        var offset = 0;
        var total = audioData.length;

        while (offset < total) {
            var len = Math.min(CHUNK_SIZE, total - offset);
            webSocket.send(ByteString.of(audioData, offset, len));
            log.debug("发送音频块，偏移: {}, 大小: {}", offset, len);
            offset += len;

            // 控制发送频率
            if (offset < total) {
                TimeUnit.MILLISECONDS.sleep(SEND_INTERVAL_MS);
            }
        }

        // 发送结束标识
        webSocket.send(ByteString.of("{\"end\":\"true\"}".getBytes()));
        log.debug("发送结束标识");

        // 等待一段时间确保数据处理完成
        TimeUnit.MILLISECONDS.sleep(1000);
        webSocket.close(1000, "识别完成");
    }

    /**
     * 处理识别响应
     */
    private void processResponses(BlockingQueue<String> responseQueue, AtomicBoolean isRunning,
                                AtomicReference<String> finalResult, CountDownLatch recognitionLatch) {
        while (isRunning.get() || !responseQueue.isEmpty()) {
            try {
                var response = responseQueue.poll(QUEUE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                if (response != null) {
                    log.debug("处理响应: {}", response);

                    JsonUtil.parse(response, RecognitionResponse.class)
                            .filter(res -> "0".equals(res.getErrorCode()))
                            .onFailure(e -> log.error("解析识别结果失败: {}", e.getMessage()))
                            .forEach(res -> {
                                if ("started".equals(res.getAction())) {
                                    log.info("有道ASR握手成功");
                                } else if ("recognition".equals(res.getAction()) && res.getResult() != null) {
                                    // 处理识别结果
                                    for (var result : res.getResult()) {
                                        if (result.getSt() != null) {
                                            for (var st : result.getSt()) {
                                                if (st.getWs() != null && !st.getWs().isEmpty()) {
                                                    var sentence = st.getWs().stream()
                                                            .map(Word::getW)
                                                            .reduce("", String::concat);
                                                    if (!sentence.trim().isEmpty()) {
                                                        log.info("识别结果: {}", sentence);
                                                        finalResult.set(sentence);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else if ("error".equals(res.getAction())) {
                                    log.error("识别过程中发生错误，错误码: {}", res.getErrorCode());
                                }
                            });
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        recognitionLatch.countDown();
    }

    /**
     * 创建请求参数
     */
    private Map<String, String[]> createRequestParams() {
        return new HashMap<>() {{
            put("langType", new String[]{"zh-CHS"});
            put("rate", new String[]{"16000"});
            put("format", new String[]{"wav"});
            put("channel", new String[]{"1"});
            put("version", new String[]{"v1"});
        }};
    }

    /**
     * 添加认证参数
     */
    private void addAuthParams(String appId, String appSecret, Map<String, String[]> paramsMap) {
        var salt = UUID.randomUUID().toString();
        var timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        var sign = generateSign(appId, salt, timestamp, appSecret);

        paramsMap.put("appKey", new String[]{appId});
        paramsMap.put("salt", new String[]{salt});
        paramsMap.put("curtime", new String[]{timestamp});
        paramsMap.put("signType", new String[]{"v4"});
        paramsMap.put("sign", new String[]{sign});
    }

    /**
     * 生成签名
     */
    private String generateSign(String appId, String salt, String timestamp, String appSecret) {
        var strSrc = appId + salt + timestamp + appSecret;
        return encrypt(strSrc);
    }

    /**
     * SHA-256加密
     */
    private String encrypt(String strSrc) {
        try {
            var md = MessageDigest.getInstance("SHA-256");
            md.update(strSrc.getBytes());
            var des = new StringBuilder();
            for (byte b : md.digest()) {
                var tmp = Integer.toHexString(b & 0xFF);
                if (tmp.length() == 1) {
                    des.append("0");
                }
                des.append(tmp);
            }
            return des.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 构建查询字符串
     */
    private String buildQueryString(Map<String, String[]> params) {
        var query = new StringBuilder();
        for (var entry : params.entrySet()) {
            var key = entry.getKey();
            var values = entry.getValue();
            for (var value : values) {
                if (query.length() > 0) {
                    query.append("&");
                }
                query.append(key).append("=").append(value);
            }
        }
        return query.toString();
    }

    /**
     * 清理资源
     */
    private void cleanup(WebSocket webSocket, OkHttpClient client) {
        try {
            if (webSocket != null) {
                webSocket.close(1000, "清理资源");
            }
            if (client != null) {
                client.dispatcher().executorService().shutdown();
                client.connectionPool().evictAll();
            }
        } catch (Exception e) {
            log.warn("清理资源时发生错误", e);
        }
    }
