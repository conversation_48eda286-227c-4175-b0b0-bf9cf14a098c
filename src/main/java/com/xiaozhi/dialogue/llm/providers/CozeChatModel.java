package com.xiaozhi.dialogue.llm.providers;

import cn.hutool.core.bean.BeanUtil;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.model.ChatEventType;
import com.coze.openapi.client.chat.model.ChatPoll;
import com.coze.openapi.client.chat.model.ChatToolCall;
import com.coze.openapi.client.connversations.CreateConversationReq;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.client.connversations.message.model.MessageType;
import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.service.CozeAPI;
import com.xiaozhi.dialogue.domain.ModelParams;
import com.xiaozhi.dialogue.llm.ChatModelService;
import com.xiaozhi.dto.ChatParams;
import com.xiaozhi.dto.CozeChatParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.metadata.ChatGenerationMetadata;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.model.MessageAggregator;
import org.springframework.ai.chat.observation.ChatModelObservationContext;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Coze Chat Model
 */
public class CozeChatModel extends ChatModelService implements ChatModel {

    private final CozeAPI coze;
    private final String botId;

    private final Logger logger = LoggerFactory.getLogger(getClass());
    public static final String PROVIDER_NAME = "coze";

    public CozeChatModel(ModelParams params) {
        this.coze = new CozeAPI.Builder()
                .baseURL(params.getEndpoint())
                .auth(new TokenAuth(params.getApiSecret()))
                .readTimeout(15 * 1000) // 15 秒超时
                .build();

        this.botId = params.getModel();
    }

    @Override
    public ChatResponse call(Prompt prompt) {
        var messages = prompt.getInstructions();
        if (messages == null || messages.isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }

        // 将消息格式转换为 Coze API 所需格式
        var cozeMessages = convertToCozeMessages(messages);

        // 创建聊天请求
        var req = CreateChatReq.builder()
                .botID(botId)
                .userID(UUID.randomUUID().toString())
                .messages(cozeMessages)
                .build();

        /*
         * Step two, poll the result of chat
         * Assume the development allows at most one chat to run for 10 seconds. If it
         * exceeds 10 seconds,
         * the chat will be cancelled.
         * And when the chat status is not completed, poll the status of the chat once
         * every second.
         * After the chat is completed, retrieve all messages in the chat.
         */
        long timeout = 10L;
        long start = System.currentTimeMillis();

        // the developer can also set the timeout.
        try {
            ChatPoll chatPoll = coze.chat().createAndPoll(req, timeout);
            logger.debug(chatPoll.toString());
            var message = chatPoll.getMessages().getLast();
            var assistantMessage = new AssistantMessage(message.getContent(), new HashMap<>(message.getMetaData()));
            var generation = new Generation(assistantMessage,
                    ChatGenerationMetadata.builder().metadata(BeanUtil.beanToMap(chatPoll.getChat())).build());
            logger.info("耗时：{}ms", System.currentTimeMillis() - start);
            return ChatResponse.builder().generations(List.of(generation)).build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public Flux<ChatResponse> stream(Prompt prompt) {
        return sendRequest(prompt, new CozeChatParams());
    }

    /**
     * 将通用消息格式转换为 Coze API所需的消息格式
     *
     * @param messages 通用格式的消息列表
     * @return Coze格式的消息列表
     */
    private List<Message> convertToCozeMessages(List<org.springframework.ai.chat.messages.Message> messages) {
        List<Message> cozeMessages = new ArrayList<>();

        for (org.springframework.ai.chat.messages.Message msg : messages) {
            Map<String, String> metadata = msg.getMetadata().entrySet()
                    .stream()
                    .filter(e -> e.getValue() != null)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            e -> e.getValue().toString()));
            switch (msg.getMessageType()) {
                case USER:
                    cozeMessages.add(Message.buildUserQuestionText(msg.getText(), metadata));
                    break;
                case ASSISTANT:
                    cozeMessages.add(Message.buildAssistantAnswer(msg.getText(), metadata));
                    break;
                default:
                    // coze 系统提示默认不在这里设定，需要在 coze 中设定
            }
        }

        return cozeMessages;
    }

    @Override
    public Flux<ChatResponse> stream(Prompt prompt, ChatParams params) {
        if (params instanceof CozeChatParams cozeChatParams) {
            var chatResponse = sendRequest(prompt, cozeChatParams);

            final ChatModelObservationContext observationContext = ChatModelObservationContext.builder()
                    .prompt(prompt)
                    .provider(PROVIDER_NAME)
                    .build();

            return new MessageAggregator().aggregate(chatResponse, observationContext::setResponse);
        }

        return Flux.empty();
    }

    private Flux<ChatResponse> sendRequest(Prompt prompt, CozeChatParams params) {
        var messages = convertToCozeMessages(prompt.getInstructions());

        var req = CreateChatReq.builder()
                .botID(this.botId)
                .userID(params.getUserId())
                .conversationID(params.getConversationId())
                .messages(messages)
                .customVariables(params.getCustomVariables())
                .build();


        try {
            return Flux.from(coze.chat().stream(req))
                    .filter(Objects::nonNull) // 过滤掉 null 事件
                    .map(event -> {
                        List<AssistantMessage.ToolCall> toolCalls = List.of();
                        String content = "";

                        if (ChatEventType.CONVERSATION_MESSAGE_DELTA.equals(event.getEvent())) {
                            Message message = event.getMessage();
                            content = Optional.ofNullable(message)
                                    .map(Message::getContent)
                                    .orElse("");
                        }

                        if (ChatEventType.CONVERSATION_CHAT_REQUIRES_ACTION.equals(event.getEvent())) {
                            List<ChatToolCall> toolCallList = event.getChat().getRequiredAction()
                                    .getSubmitToolOutputs().getToolCalls();

                            toolCalls = toolCallList
                                    .stream()
                                    .map(toolCall -> new AssistantMessage.ToolCall(
                                            toolCall.getID(),
                                            "function",
                                            toolCall.getFunction().getName(),
                                            toolCall.getFunction().getArguments()))
                                    .toList();
                        }

                        if (ChatEventType.CONVERSATION_CHAT_COMPLETED.equals(event.getEvent())) {
                            Message message = event.getMessage();
                            if (message != null && MessageType.FOLLOW_UP.equals(message.getType())) {
                                logger.debug(message.getContent());
                            } else if (event.getChat() != null && event.getChat().getUsage() != null) {
                                logger.debug("Token usage:{}", event.getChat().getUsage().getTokenCount());
                            }
                        }

                        if (ChatEventType.DONE.equals(event.getEvent())) {
                            logger.info("Coze chat completed");
                            // coze.shutdownExecutor();
                        }

                        var message = event.getMessage();

                        Map<String, Object> messageMetadata = Optional.ofNullable(message)
                                .map(Message::getMetaData)
                                .map(metaData -> {
                                    Map<String, Object> result = new HashMap<>();
                                    if (metaData != null) {
                                        result.putAll(metaData);
                                    }
                                    return result;
                                })
                                .orElse(new HashMap<>());

                        var assistantMessage = new AssistantMessage(content, messageMetadata, toolCalls);

                        Map<String, Object> chatMetadata = Optional.ofNullable(event.getChat())
                                .map(chat -> {
                                    Map<String, Object> beanMap = BeanUtil.beanToMap(chat);
                                    // 过滤掉 null 值
                                    return beanMap.entrySet().stream()
                                            .filter(entry -> entry.getValue() != null)
                                            .collect(Collectors.toMap(
                                                    Map.Entry::getKey,
                                                    Map.Entry::getValue));
                                })
                                .orElse(new HashMap<>());

                        ChatGenerationMetadata generationMetadata = ChatGenerationMetadata.builder()
                                .metadata(chatMetadata)
                                .build();

                        var generation = new Generation(assistantMessage, generationMetadata);
                        return new ChatResponse(List.of(generation));
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public String getConversationId(ChatParams params) {
        var resp = this.coze.conversations().create(
                CreateConversationReq.builder()
                        .botID(botId)
                        .build()
        );
        return resp.getConversation().getId();
    }
}